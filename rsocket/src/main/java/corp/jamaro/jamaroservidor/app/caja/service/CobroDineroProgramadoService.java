package corp.jamaro.jamaroservidor.app.caja.service;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.caja.repository.CajaRepository;
import corp.jamaro.jamaroservidor.app.caja.repository.CobroDineroProgramadoRepository;
import corp.jamaro.jamaroservidor.app.dinero.model.Dinero;
import corp.jamaro.jamaroservidor.app.dinero.repository.DineroRepository;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import corp.jamaro.jamaroservidor.app.ventas.repository.SaleRepository;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.UUID;

/**
 * Servicio para realizar operaciones de cobro en CobroDineroProgramado.
 * Maneja la creación de Dinero, actualización de montos y relaciones con Caja.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CobroDineroProgramadoService {

    private final CobroDineroProgramadoRepository cobroDineroProgramadoRepository;
    private final DineroRepository dineroRepository;
    private final CajaRepository cajaRepository;
    private final SaleRepository saleRepository;
    private final CobroDineroProgramadoGuiService guiService;
    private final TransactionalOperator transactionalOperator;

    /**
     * Realiza un cobro para un CobroDineroProgramado específico.
     * 
     * Lógica de negocio:
     * 1. Valida que el cobro existe y no está completamente cobrado
     * 2. Crea el Dinero con esEntrada=true y tramitadoPor del usuario actual
     * 3. Relaciona el Dinero con la Caja de entradas
     * 4. Relaciona el Dinero con el CobroDineroProgramado
     * 5. Actualiza montoRestante del CobroDineroProgramado
     * 6. Si montoRestante llega a 0, marca estaCobrado=true y establece terminadoDeCobrarEl
     * 7. Actualiza totalRestante del Sale
     * 8. Si Sale está completamente pagado, marca estaPagadoEntregado=true
     * 9. Emite actualización de la GUI
     * 
     * @param cobroId ID del CobroDineroProgramado
     * @param cajaId ID de la Caja donde se registra la entrada
     * @param montoCobrado Monto a cobrar
     * @param tipoDeDinero Tipo de dinero (EFECTIVO/DIGITAL)
     * @param detalles Detalles del pago (opcional)
     * @return Mono<Boolean> true si el cobro fue exitoso
     */
    public Mono<Boolean> realizarCobro(UUID cobroId, UUID cajaId, Double montoCobrado, 
                                      Dinero.TipoDeDinero tipoDeDinero, String detalles) {
        log.info("Iniciando cobro para CobroDineroProgramado {} por monto {}", cobroId, montoCobrado);

        // Validaciones fuera de la transacción
        return validateCobroRequest(cobroId, montoCobrado)
            .flatMap(valid -> SecurityUtils.getCurrentUser())
            .flatMap(user -> {
                String tramitadoPor = user.getUsername();
                
                // OPERACIÓN TRANSACCIONAL
                return cobroDineroProgramadoRepository.findCobroWithSale(cobroId)
                    .flatMap(projection -> {
                        // Usar proyección automática de Spring Data Neo4j
                        CobroDineroProgramado cobro = projection.getCobro();
                        Sale sale = projection.getSale();

                        // Validar que el monto no exceda lo pendiente
                        if (montoCobrado > cobro.getMontoRestante()) {
                            return Mono.error(new IllegalArgumentException(
                                "El monto a cobrar excede el monto restante"));
                        }

                        // Crear Dinero
                        Dinero dinero = createDinero(montoCobrado, tipoDeDinero, detalles, tramitadoPor);

                        return dineroRepository.save(dinero)
                            .flatMap(savedDinero -> {
                                // Crear relaciones y actualizar montos
                                return createDineroRelations(savedDinero.getId(), cajaId, cobroId)
                                    .then(updateCobroMontos(cobro, montoCobrado))
                                    .then(updateSaleMontos(sale, montoCobrado))
                                    .thenReturn(true);
                            });
                    })
                    .as(transactionalOperator::transactional);
            })
            .flatMap(success -> {
                // Emitir actualización de GUI después de la transacción
                return guiService.emitGuiUpdate().thenReturn(success);
            })
            .onErrorResume(e -> {
                log.error("Error al realizar cobro: {}", e.getMessage());
                return Mono.just(false);
            });
    }

    /**
     * Valida la solicitud de cobro.
     */
    private Mono<Boolean> validateCobroRequest(UUID cobroId, Double montoCobrado) {
        if (montoCobrado == null || montoCobrado <= 0) {
            return Mono.error(new IllegalArgumentException("El monto debe ser mayor a 0"));
        }
        
        return cobroDineroProgramadoRepository.findById(cobroId)
            .switchIfEmpty(Mono.error(new IllegalArgumentException("CobroDineroProgramado no encontrado")))
            .flatMap(cobro -> {
                if (cobro.getEstaCobrado()) {
                    return Mono.error(new IllegalArgumentException("El cobro ya está completamente pagado"));
                }
                return Mono.just(true);
            });
    }

    /**
     * Crea un objeto Dinero con los datos proporcionados.
     */
    private Dinero createDinero(Double monto, Dinero.TipoDeDinero tipoDeDinero, 
                               String detalles, String tramitadoPor) {
        Dinero dinero = new Dinero();
        dinero.setTramitadoPor(tramitadoPor);
        dinero.setMontoAntesDelCambio(monto);
        dinero.setFactorDeCambio(1.0); // Por defecto en soles
        dinero.setMontoReal(monto);
        dinero.setTipoDeDinero(tipoDeDinero);
        dinero.setDetalles(detalles);
        dinero.setEsEntrada(true); // Es una entrada de dinero
        dinero.setCreatedAt(Instant.now());
        return dinero;
    }

    /**
     * Crea las relaciones del Dinero con Caja y CobroDineroProgramado.
     */
    private Mono<Void> createDineroRelations(UUID dineroId, UUID cajaId, UUID cobroId) {
        log.info("Creando relaciones para Dinero {} con Caja {} y Cobro {}", dineroId, cajaId, cobroId);

        return cobroDineroProgramadoRepository.createCajaDineroRelation(cajaId, dineroId)
            .then(cobroDineroProgramadoRepository.createCobroDineroRelation(cobroId, dineroId))
            .then();
    }

    /**
     * Actualiza los montos del CobroDineroProgramado.
     */
    private Mono<Void> updateCobroMontos(CobroDineroProgramado cobro, Double montoCobrado) {
        Double nuevoMontoRestante = cobro.getMontoRestante() - montoCobrado;

        log.info("Actualizando montos del cobro. Nuevo monto restante: {}", nuevoMontoRestante);

        return cobroDineroProgramadoRepository.updateCobroMontos(
            cobro.getId(), nuevoMontoRestante, Instant.now()
        ).then();
    }

    /**
     * Actualiza los montos del Sale.
     */
    private Mono<Void> updateSaleMontos(Sale sale, Double montoCobrado) {
        Double nuevoTotalRestante = sale.getTotalRestante() - montoCobrado;

        log.info("Actualizando montos del sale. Nuevo total restante: {}", nuevoTotalRestante);

        return saleRepository.updateSaleMontos(sale.getId(), nuevoTotalRestante).then();
    }


}
