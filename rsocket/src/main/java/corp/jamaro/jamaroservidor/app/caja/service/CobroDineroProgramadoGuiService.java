package corp.jamaro.jamaroservidor.app.caja.service;

import corp.jamaro.jamaroservidor.app.caja.controller.CobroDineroProgramadoGuiController.CobroPendientePair;
import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.caja.projection.CobroPendienteProjection;
import corp.jamaro.jamaroservidor.app.caja.repository.CobroDineroProgramadoRepository;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

/**
 * Servicio para manejar la GUI de CobroDineroProgramado en tiempo real.
 * Proporciona suscripciones reactivas separadas para cada tipo de venta.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CobroDineroProgramadoGuiService {

    private final CobroDineroProgramadoRepository cobroDineroProgramadoRepository;

    // Sinks separados para cada tipo de venta
    private final Sinks.Many<CobroPendientePair> contadoSink =
        Sinks.many().multicast().directBestEffort();

    private final Sinks.Many<CobroPendientePair> creditoSink =
        Sinks.many().multicast().directBestEffort();

    private final Sinks.Many<CobroPendientePair> pedidoSink =
        Sinks.many().multicast().directBestEffort();

    /**
     * Se suscribe a los cambios de cobros CONTADO pendientes.
     * Retorna el estado actual y luego las actualizaciones en tiempo real.
     */
    public Flux<CobroPendientePair> subscribeToContadoChanges() {
        log.info("Nueva suscripción a cambios de cobros CONTADO");

        // Obtener estado actual de contado
        Flux<CobroPendientePair> currentState = getCurrentContadoState();

        // Flujo de actualizaciones futuras
        Flux<CobroPendientePair> updates = contadoSink.asFlux();

        // Concatenar estado actual con actualizaciones
        return currentState.concatWith(updates);
    }

    /**
     * Se suscribe a los cambios de cobros CREDITO pendientes.
     * Retorna el estado actual y luego las actualizaciones en tiempo real.
     */
    public Flux<CobroPendientePair> subscribeToCreditoChanges() {
        log.info("Nueva suscripción a cambios de cobros CREDITO");

        // Obtener estado actual de crédito (primera página)
        Flux<CobroPendientePair> currentState = getCurrentCreditoState(0, 30);

        // Flujo de actualizaciones futuras
        Flux<CobroPendientePair> updates = creditoSink.asFlux();

        // Concatenar estado actual con actualizaciones
        return currentState.concatWith(updates);
    }

    /**
     * Se suscribe a los cambios de cobros PEDIDO pendientes.
     * Retorna el estado actual y luego las actualizaciones en tiempo real.
     */
    public Flux<CobroPendientePair> subscribeToPedidoChanges() {
        log.info("Nueva suscripción a cambios de cobros PEDIDO");

        // Obtener estado actual de pedido
        Flux<CobroPendientePair> currentState = getCurrentPedidoState();

        // Flujo de actualizaciones futuras
        Flux<CobroPendientePair> updates = pedidoSink.asFlux();

        // Concatenar estado actual con actualizaciones
        return currentState.concatWith(updates);
    }

    /**
     * Obtiene los cobros de crédito con paginación (lazy loading).
     */
    public Flux<CobroPendientePair> getCreditosPage(int page, int size) {
        log.info("Obteniendo página {} de créditos pendientes (tamaño: {})", page, size);

        int skip = page * size;

        return cobroDineroProgramadoRepository.findCobrosCreditoPendientesWithSale(skip, size)
            .map(this::convertProjectionToPair);
    }

    /**
     * Emite actualizaciones para todos los tipos de venta.
     * Se llama cuando hay cambios en los cobros pendientes.
     */
    public Mono<Void> emitGuiUpdate() {
        log.debug("Emitiendo actualización de CobroDineroProgramadoGui para todos los tipos");

        return Mono.when(
            emitContadoUpdates(),
            emitCreditoUpdates(),
            emitPedidoUpdates()
        );
    }

    /**
     * Emite actualizaciones solo para cobros CONTADO.
     */
    public Mono<Void> emitContadoUpdates() {
        return getCurrentContadoState()
            .doOnNext(pair -> {
                contadoSink.tryEmitNext(pair);
                log.debug("Actualización CONTADO emitida: {}", pair.cobro().getId());
            })
            .then();
    }

    /**
     * Emite actualizaciones solo para cobros CREDITO.
     */
    public Mono<Void> emitCreditoUpdates() {
        return getCurrentCreditoState(0, 30)
            .doOnNext(pair -> {
                creditoSink.tryEmitNext(pair);
                log.debug("Actualización CREDITO emitida: {}", pair.cobro().getId());
            })
            .then();
    }

    /**
     * Emite actualizaciones solo para cobros PEDIDO.
     */
    public Mono<Void> emitPedidoUpdates() {
        return getCurrentPedidoState()
            .doOnNext(pair -> {
                pedidoSink.tryEmitNext(pair);
                log.debug("Actualización PEDIDO emitida: {}", pair.cobro().getId());
            })
            .then();
    }

    /**
     * Obtiene el estado actual de cobros CONTADO.
     */
    private Flux<CobroPendientePair> getCurrentContadoState() {
        return cobroDineroProgramadoRepository.findCobrosContadoPendientesWithSale()
            .map(this::convertProjectionToPair);
    }

    /**
     * Obtiene el estado actual de cobros CREDITO con paginación.
     */
    private Flux<CobroPendientePair> getCurrentCreditoState(int page, int size) {
        int skip = page * size;
        return cobroDineroProgramadoRepository.findCobrosCreditoPendientesWithSale(skip, size)
            .map(this::convertProjectionToPair);
    }

    /**
     * Obtiene el estado actual de cobros PEDIDO.
     */
    private Flux<CobroPendientePair> getCurrentPedidoState() {
        return cobroDineroProgramadoRepository.findCobrosPedidoPendientesWithSale()
            .map(this::convertProjectionToPair);
    }

    /**
     * Obtiene todos los cobros pendientes de un Cliente específico para ventas CONTADO.
     */
    public Flux<CobroPendientePair> getCobrosPendientesContadoByCliente(UUID clienteId) {
        log.info("Obteniendo cobros CONTADO pendientes para cliente: {}", clienteId);

        return cobroDineroProgramadoRepository.findCobrosContadoPendientesByCliente(clienteId)
            .map(this::convertProjectionToPair);
    }

    /**
     * Obtiene todos los cobros pendientes de un Cliente específico para ventas CREDITO.
     */
    public Flux<CobroPendientePair> getCobrosPendientesCreditoByCliente(UUID clienteId) {
        log.info("Obteniendo cobros CREDITO pendientes para cliente: {}", clienteId);

        return cobroDineroProgramadoRepository.findCobrosCreditoPendientesByCliente(clienteId)
            .map(this::convertProjectionToPair);
    }

    /**
     * Obtiene todos los cobros pendientes de un Cliente específico para ventas PEDIDO.
     */
    public Flux<CobroPendientePair> getCobrosPendientesPedidoByCliente(UUID clienteId) {
        log.info("Obteniendo cobros PEDIDO pendientes para cliente: {}", clienteId);

        return cobroDineroProgramadoRepository.findCobrosPedidoPendientesByCliente(clienteId)
            .map(this::convertProjectionToPair);
    }

    /**
     * Obtiene todos los cobros pendientes de un Cliente específico para todos los tipos de venta.
     * Incluye CONTADO, CREDITO y PEDIDO.
     */
    public Flux<CobroPendientePair> getCobrosPendientesByCliente(UUID clienteId) {
        log.info("Obteniendo todos los cobros pendientes para cliente: {}", clienteId);

        return cobroDineroProgramadoRepository.findCobrosPendientesByCliente(clienteId)
            .map(this::convertProjectionToPair);
    }

    /**
     * Convierte una proyección de Spring Data Neo4j a CobroPendientePair.
     * Utiliza las capacidades out-of-the-box de Spring Data Neo4j para mapeo automático.
     */
    private CobroPendientePair convertProjectionToPair(CobroPendienteProjection projection) {
        return new CobroPendientePair(projection.getCobro(), projection.getSale());
    }
}
