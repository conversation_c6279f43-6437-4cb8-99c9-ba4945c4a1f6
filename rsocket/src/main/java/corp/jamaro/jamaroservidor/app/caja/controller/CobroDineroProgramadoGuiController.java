package corp.jamaro.jamaroservidor.app.caja.controller;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.caja.service.CobroDineroProgramadoGuiService;
import corp.jamaro.jamaroservidor.app.caja.service.CobroDineroProgramadoService;
import corp.jamaro.jamaroservidor.app.dinero.model.Dinero;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import corp.jamaro.jamaroservidor.security.enums.RoleEnum;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Controlador RSocket para la GUI de CobroDineroProgramado.
 * Accesible solo para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
 * Proporciona 3 endpoints de suscripción separados por tipo de venta.
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class CobroDineroProgramadoGuiController {

    private final CobroDineroProgramadoGuiService cobroDineroProgramadoGuiService;
    private final CobroDineroProgramadoService cobroDineroProgramadoService;

    /**
     * Permite suscribirse a los cambios de cobros CONTADO pendientes.
     * Solo accesible para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
     *
     * @return Flux que emite pares <CobroDineroProgramado, Sale> para ventas CONTADO
     */
    @MessageMapping("cobroDineroProgramadoGui.subscribeContado")
    public Flux<CobroPendientePair> subscribeToContadoChanges() {
        log.info("Solicitud de suscripción a cobros CONTADO recibida");

        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
            .flatMapMany(hasPermission -> {
                if (!hasPermission) {
                    log.warn("Acceso denegado: usuario sin permisos para acceder a cobros CONTADO");
                    return Flux.error(new SecurityException("Acceso denegado: permisos insuficientes"));
                }

                return SecurityUtils.getCurrentUser()
                    .doOnNext(user -> log.info("Usuario {} suscribiéndose a cobros CONTADO", user.getUsername()))
                    .flatMapMany(user -> cobroDineroProgramadoGuiService.subscribeToContadoChanges());
            });
    }

    /**
     * Permite suscribirse a los cambios de cobros CREDITO pendientes.
     * Solo accesible para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
     *
     * @return Flux que emite pares <CobroDineroProgramado, Sale> para ventas CREDITO
     */
    @MessageMapping("cobroDineroProgramadoGui.subscribeCredito")
    public Flux<CobroPendientePair> subscribeToCreditoChanges() {
        log.info("Solicitud de suscripción a cobros CREDITO recibida");

        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
            .flatMapMany(hasPermission -> {
                if (!hasPermission) {
                    log.warn("Acceso denegado: usuario sin permisos para acceder a cobros CREDITO");
                    return Flux.error(new SecurityException("Acceso denegado: permisos insuficientes"));
                }

                return SecurityUtils.getCurrentUser()
                    .doOnNext(user -> log.info("Usuario {} suscribiéndose a cobros CREDITO", user.getUsername()))
                    .flatMapMany(user -> cobroDineroProgramadoGuiService.subscribeToCreditoChanges());
            });
    }

    /**
     * Permite suscribirse a los cambios de cobros PEDIDO pendientes.
     * Solo accesible para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
     *
     * @return Flux que emite pares <CobroDineroProgramado, Sale> para ventas PEDIDO
     */
    @MessageMapping("cobroDineroProgramadoGui.subscribePedido")
    public Flux<CobroPendientePair> subscribeToPedidoChanges() {
        log.info("Solicitud de suscripción a cobros PEDIDO recibida");

        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
            .flatMapMany(hasPermission -> {
                if (!hasPermission) {
                    log.warn("Acceso denegado: usuario sin permisos para acceder a cobros PEDIDO");
                    return Flux.error(new SecurityException("Acceso denegado: permisos insuficientes"));
                }

                return SecurityUtils.getCurrentUser()
                    .doOnNext(user -> log.info("Usuario {} suscribiéndose a cobros PEDIDO", user.getUsername()))
                    .flatMapMany(user -> cobroDineroProgramadoGuiService.subscribeToPedidoChanges());
            });
    }

    /**
     * Obtiene una página específica de cobros de crédito (lazy loading).
     * Solo accesible para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
     *
     * @param request Objeto con información de paginación
     * @return Flux que emite pares <CobroDineroProgramado, Sale> para la página solicitada
     */
    @MessageMapping("cobroDineroProgramadoGui.getCreditosPage")
    public Flux<CobroPendientePair> getCreditosPage(@Payload CreditoPageRequest request) {
        log.info("Solicitud de página {} de créditos (tamaño: {})", request.page(), request.size());

        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
            .flatMapMany(hasPermission -> {
                if (!hasPermission) {
                    log.warn("Acceso denegado: usuario sin permisos para obtener página de créditos");
                    return Flux.error(new SecurityException("Acceso denegado: permisos insuficientes"));
                }

                return SecurityUtils.getCurrentUser()
                    .doOnNext(user -> log.info("Usuario {} solicitando página {} de créditos",
                        user.getUsername(), request.page()))
                    .flatMapMany(user -> cobroDineroProgramadoGuiService.getCreditosPage(request.page(), request.size()));
            });
    }

    /**
     * Fuerza una actualización manual de la GUI.
     * Solo accesible para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
     * 
     * @return Mono que completa cuando la actualización se emite
     */
    @MessageMapping("cobroDineroProgramadoGui.refresh")
    public Mono<Void> refreshGui() {
        log.info("Solicitud de actualización manual de CobroDineroProgramadoGui");
        
        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
            .flatMap(hasPermission -> {
                if (!hasPermission) {
                    log.warn("Acceso denegado: usuario sin permisos para refrescar GUI");
                    return Mono.error(new SecurityException("Acceso denegado: permisos insuficientes"));
                }
                
                return SecurityUtils.getCurrentUser()
                    .doOnNext(user -> log.info("Usuario {} solicitando refresh de GUI", user.getUsername()))
                    .flatMap(user -> cobroDineroProgramadoGuiService.emitGuiUpdate());
            });
    }

    /**
     * Realiza un cobro para un CobroDineroProgramado específico.
     * Solo accesible para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
     *
     * @param request Datos del cobro a realizar
     * @return Mono que emite true si el cobro fue exitoso
     */
    @MessageMapping("cobroDineroProgramadoGui.realizarCobro")
    public Mono<Boolean> realizarCobro(@Payload CobroRequest request) {
        log.info("Solicitud de cobro para CobroDineroProgramado {} por monto {}",
            request.cobroId(), request.montoCobrado());

        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
            .flatMap(hasPermission -> {
                if (!hasPermission) {
                    log.warn("Acceso denegado: usuario sin permisos para realizar cobros");
                    return Mono.error(new SecurityException("Acceso denegado: permisos insuficientes"));
                }

                return SecurityUtils.getCurrentUser()
                    .doOnNext(user -> log.info("Usuario {} realizando cobro para CobroDineroProgramado {}",
                        user.getUsername(), request.cobroId()))
                    .flatMap(user -> cobroDineroProgramadoService.realizarCobro(
                        request.cobroId(),
                        request.cajaId(),
                        request.montoCobrado(),
                        request.tipoDeDinero(),
                        request.detalles()
                    ));
            });
    }

    /**
     * Obtiene todos los cobros CONTADO pendientes de un Cliente específico.
     * Solo accesible para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
     *
     * @param request Objeto con el ID del cliente
     * @return Flux que emite pares <CobroDineroProgramado, Sale> para ventas CONTADO del cliente
     */
    @MessageMapping("cobroDineroProgramadoGui.getCobrosPendientesContadoByCliente")
    public Flux<CobroPendientePair> getCobrosPendientesContadoByCliente(@Payload ClienteRequest request) {
        log.info("Solicitud de cobros CONTADO pendientes para cliente: {}", request.clienteId());

        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
            .flatMapMany(hasPermission -> {
                if (!hasPermission) {
                    log.warn("Acceso denegado: usuario sin permisos para obtener cobros por cliente");
                    return Flux.error(new SecurityException("Acceso denegado: permisos insuficientes"));
                }

                return SecurityUtils.getCurrentUser()
                    .doOnNext(user -> log.info("Usuario {} obteniendo cobros CONTADO para cliente {}",
                        user.getUsername(), request.clienteId()))
                    .flatMapMany(user -> cobroDineroProgramadoGuiService.getCobrosPendientesContadoByCliente(request.clienteId()));
            });
    }

    /**
     * Obtiene todos los cobros CREDITO pendientes de un Cliente específico.
     * Solo accesible para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
     *
     * @param request Objeto con el ID del cliente
     * @return Flux que emite pares <CobroDineroProgramado, Sale> para ventas CREDITO del cliente
     */
    @MessageMapping("cobroDineroProgramadoGui.getCobrosPendientesCreditoByCliente")
    public Flux<CobroPendientePair> getCobrosPendientesCreditoByCliente(@Payload ClienteRequest request) {
        log.info("Solicitud de cobros CREDITO pendientes para cliente: {}", request.clienteId());

        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
            .flatMapMany(hasPermission -> {
                if (!hasPermission) {
                    log.warn("Acceso denegado: usuario sin permisos para obtener cobros por cliente");
                    return Flux.error(new SecurityException("Acceso denegado: permisos insuficientes"));
                }

                return SecurityUtils.getCurrentUser()
                    .doOnNext(user -> log.info("Usuario {} obteniendo cobros CREDITO para cliente {}",
                        user.getUsername(), request.clienteId()))
                    .flatMapMany(user -> cobroDineroProgramadoGuiService.getCobrosPendientesCreditoByCliente(request.clienteId()));
            });
    }

    /**
     * Obtiene todos los cobros PEDIDO pendientes de un Cliente específico.
     * Solo accesible para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
     *
     * @param request Objeto con el ID del cliente
     * @return Flux que emite pares <CobroDineroProgramado, Sale> para ventas PEDIDO del cliente
     */
    @MessageMapping("cobroDineroProgramadoGui.getCobrosPendientesPedidoByCliente")
    public Flux<CobroPendientePair> getCobrosPendientesPedidoByCliente(@Payload ClienteRequest request) {
        log.info("Solicitud de cobros PEDIDO pendientes para cliente: {}", request.clienteId());

        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
            .flatMapMany(hasPermission -> {
                if (!hasPermission) {
                    log.warn("Acceso denegado: usuario sin permisos para obtener cobros por cliente");
                    return Flux.error(new SecurityException("Acceso denegado: permisos insuficientes"));
                }

                return SecurityUtils.getCurrentUser()
                    .doOnNext(user -> log.info("Usuario {} obteniendo cobros PEDIDO para cliente {}",
                        user.getUsername(), request.clienteId()))
                    .flatMapMany(user -> cobroDineroProgramadoGuiService.getCobrosPendientesPedidoByCliente(request.clienteId()));
            });
    }

    /**
     * Obtiene todos los cobros pendientes de un Cliente específico para todos los tipos de venta.
     * Solo accesible para usuarios con roles ADMIN, MANAGER o ADMINISTRATOR.
     *
     * @param request Objeto con el ID del cliente
     * @return Flux que emite pares <CobroDineroProgramado, Sale> para todas las ventas del cliente
     */
    @MessageMapping("cobroDineroProgramadoGui.getCobrosPendientesByCliente")
    public Flux<CobroPendientePair> getCobrosPendientesByCliente(@Payload ClienteRequest request) {
        log.info("Solicitud de todos los cobros pendientes para cliente: {}", request.clienteId());

        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
            .flatMapMany(hasPermission -> {
                if (!hasPermission) {
                    log.warn("Acceso denegado: usuario sin permisos para obtener cobros por cliente");
                    return Flux.error(new SecurityException("Acceso denegado: permisos insuficientes"));
                }

                return SecurityUtils.getCurrentUser()
                    .doOnNext(user -> log.info("Usuario {} obteniendo todos los cobros para cliente {}",
                        user.getUsername(), request.clienteId()))
                    .flatMapMany(user -> cobroDineroProgramadoGuiService.getCobrosPendientesByCliente(request.clienteId()));
            });
    }

    /**
     * Record para la solicitud de paginación de créditos.
     */
    public record CreditoPageRequest(int page, int size) {
        public CreditoPageRequest {
            if (page < 0) {
                throw new IllegalArgumentException("La página no puede ser negativa");
            }
            if (size <= 0 || size > 100) {
                throw new IllegalArgumentException("El tamaño debe estar entre 1 y 100");
            }
        }
    }

    /**
     * Record que representa un par de CobroDineroProgramado y Sale.
     * Se usa para transmitir los datos de cobros pendientes con su venta asociada.
     */
    public record CobroPendientePair(
        CobroDineroProgramado cobro,
        Sale sale
    ) {
        public CobroPendientePair {
            if (cobro == null) {
                throw new IllegalArgumentException("CobroDineroProgramado no puede ser null");
            }
            if (sale == null) {
                throw new IllegalArgumentException("Sale no puede ser null");
            }
        }
    }

    /**
     * Record para la solicitud de cobro.
     */
    public record CobroRequest(
        java.util.UUID cobroId,
        java.util.UUID cajaId,
        Double montoCobrado,
        Dinero.TipoDeDinero tipoDeDinero,
        String detalles
    ) {
        public CobroRequest {
            if (cobroId == null) {
                throw new IllegalArgumentException("El ID del cobro es requerido");
            }
            if (cajaId == null) {
                throw new IllegalArgumentException("El ID de la caja es requerido");
            }
            if (montoCobrado == null || montoCobrado <= 0) {
                throw new IllegalArgumentException("El monto debe ser mayor a 0");
            }
            if (tipoDeDinero == null) {
                throw new IllegalArgumentException("El tipo de dinero es requerido");
            }
        }
    }
}
