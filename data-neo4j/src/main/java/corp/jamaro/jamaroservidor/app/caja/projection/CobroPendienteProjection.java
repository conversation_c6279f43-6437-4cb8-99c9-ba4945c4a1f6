package corp.jamaro.jamaroservidor.app.caja.projection;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.model.Cliente;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;

/**
 * Proyección para consultas de cobros pendientes que incluyen CobroDineroProgramado, Sale y Cliente.
 * Utiliza las capacidades out-of-the-box de Spring Data Neo4j para mapeo automático.
 * 
 * Esta interfaz reemplaza el mapeo manual que se hacía en CobroDineroProgramadoGuiService
 * con el método extractCobroPendientePairFromMap().
 */
public interface CobroPendienteProjection {
    
    /**
     * @return El CobroDineroProgramado pendiente
     */
    CobroDineroProgramado getCobro();
    
    /**
     * @return El Sale relacionado con el cobro
     */
    Sale getSale();
    
    /**
     * @return El Cliente relacionado con el Sale (puede ser null)
     */
    Cliente getCliente();
}
