package corp.jamaro.jamaroservidor.app.caja.projection;

import corp.jamaro.jamaroservidor.app.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.model.Cliente;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;

/**
 * Proyección para consultas que retornan CobroDineroProgramado con Sale y Cliente relacionados.
 * Utiliza las capacidades out-of-the-box de Spring Data Neo4j para mapeo automático.
 * 
 * Esta interfaz reemplaza el mapeo manual de Map<String, Object> que se hacía anteriormente
 * en métodos como extractCobroFromMap() y extractSaleFromMap().
 */
public interface CobroWithSaleProjection {
    
    /**
     * @return El CobroDineroProgramado de la consulta
     */
    CobroDineroProgramado getCobro();
    
    /**
     * @return El Sale relacionado con el CobroDineroProgramado
     */
    Sale getSale();
    
    /**
     * @return El Cliente relacionado con el Sale (puede ser null)
     */
    Cliente getCliente();
}
